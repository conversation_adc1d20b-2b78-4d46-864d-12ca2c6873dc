#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试GPU检查功能的脚本
"""

import sys
import os
import subprocess

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ffmpeg_path():
    """测试ffmpeg路径查找"""
    print("=" * 50)
    print("测试ffmpeg路径查找")
    print("=" * 50)
    
    # 方法1: 直接调用
    try:
        result = subprocess.run('ffmpeg -version', shell=True, capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✓ ffmpeg在系统PATH中可用")
            version_line = result.stdout.split('\n')[0]
            print(f"版本信息: {version_line}")
        else:
            print("❌ ffmpeg不在系统PATH中")
    except Exception as e:
        print(f"❌ 调用ffmpeg失败: {e}")

def test_nvidia_driver():
    """测试NVIDIA驱动"""
    print("\n" + "=" * 50)
    print("测试NVIDIA驱动")
    print("=" * 50)
    
    try:
        result = subprocess.run('nvidia-smi', shell=True, capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✓ NVIDIA驱动已安装")
            lines = result.stdout.split('\n')
            for line in lines[:3]:  # 显示前3行
                if line.strip():
                    print(f"  {line}")
        else:
            print("❌ NVIDIA驱动未安装或不可用")
    except Exception as e:
        print(f"❌ 无法检查NVIDIA驱动: {e}")

def test_ffmpeg_encoders():
    """测试ffmpeg编码器"""
    print("\n" + "=" * 50)
    print("测试ffmpeg编码器")
    print("=" * 50)
    
    try:
        result = subprocess.run('ffmpeg -hide_banner -encoders', shell=True, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            output = result.stdout.lower()
            print(f"✓ ffmpeg编码器检查完成，输出长度: {len(output)}")
            
            # 查找NVIDIA编码器
            nvenc_encoders = []
            lines = output.split('\n')
            for line in lines:
                if 'nvenc' in line or ('h264' in line and 'nv' in line):
                    nvenc_encoders.append(line.strip())
            
            if nvenc_encoders:
                print("✓ 检测到NVIDIA GPU编码器:")
                for encoder in nvenc_encoders:
                    print(f"  - {encoder}")
            else:
                print("❌ 未检测到NVIDIA GPU编码器")
                # 显示所有H264编码器
                h264_encoders = [line.strip() for line in lines if 'h264' in line and line.strip()]
                if h264_encoders:
                    print("可用的H264编码器:")
                    for encoder in h264_encoders[:5]:  # 只显示前5个
                        print(f"  - {encoder}")
        else:
            print(f"❌ ffmpeg编码器检查失败，返回码: {result.returncode}")
            print(f"错误输出: {result.stderr}")
    except Exception as e:
        print(f"❌ 检查ffmpeg编码器失败: {e}")

def test_nvenc_encoding():
    """测试NVENC编码"""
    print("\n" + "=" * 50)
    print("测试NVENC编码")
    print("=" * 50)
    
    try:
        cmd = 'ffmpeg -f lavfi -i testsrc=duration=1:size=320x240:rate=1 -c:v h264_nvenc -f null -'
        print(f"执行命令: {cmd}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✓ NVIDIA编码器测试成功")
        else:
            print("❌ NVIDIA编码器测试失败")
            print(f"错误信息: {result.stderr}")
    except Exception as e:
        print(f"❌ 测试NVENC编码失败: {e}")

def main():
    """主函数"""
    print("GPU支持检查工具")
    print("此工具将帮助诊断为什么GPU模式不工作")
    
    test_ffmpeg_path()
    test_nvidia_driver()
    test_ffmpeg_encoders()
    test_nvenc_encoding()
    
    print("\n" + "=" * 50)
    print("检查完成")
    print("=" * 50)
    print("如果所有测试都通过，GPU模式应该可以工作")
    print("如果有测试失败，请根据错误信息进行相应的安装或配置")

if __name__ == '__main__':
    main()
